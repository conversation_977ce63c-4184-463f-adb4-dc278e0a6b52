<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'lesson_list',
        'code',
        'auto_manage_materials',
        'max_materials',
        'materials_add_count',
        'materials_update_frequency',
        'last_materials_update',
        'enable_title_prefix',
        'title_prefix_format'
    ];

    /**
     * Get the material list from the lesson_list field
     *
     * This accessor converts the comma-separated string stored in the database
     * into an array of material identifiers for use in the application.
     *
     * @return array
     */
    public function getMaterialListAttribute()
    {
        if (empty($this->lesson_list)) {
            return [];
        }

        return explode(',', $this->lesson_list);
    }

    /**
     * Set the lesson_list attribute
     *
     * This mutator ensures consistent formatting when saving lesson_list data.
     * It handles both array and string inputs and normalizes them to a comma-separated string.
     *
     * @param mixed $value
     * @return void
     */
    public function setLessonListAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['lesson_list'] = implode(',', array_filter($value));
        } else {
            $this->attributes['lesson_list'] = $value;
        }
    }

    /**
     * Get the count of materials in the lesson_list
     *
     * @return int
     */
    public function getMaterialCountAttribute()
    {
        return count($this->material_list);
    }

    /**
     * Check if the account type is due for a material update
     *
     * @return bool
     */
    public function isDueForMaterialUpdate()
    {
        if (!$this->auto_manage_materials) {
            return false;
        }

        if (!$this->last_materials_update) {
            return true;
        }

        $lastUpdate = new \DateTime($this->last_materials_update);
        $now = new \DateTime();

        switch ($this->materials_update_frequency) {
            case 'daily':
                $interval = new \DateInterval('P1D');
                break;
            case 'weekly':
                $interval = new \DateInterval('P7D');
                break;
            case 'monthly':
                $interval = new \DateInterval('P1M');
                break;
            default:
                $interval = new \DateInterval('P7D');
        }

        $lastUpdate->add($interval);

        return $now >= $lastUpdate;
    }

    /**
     * Get the next update date based on the last update and frequency.
     * For monthly, next update is the same day next month. For custom, use days.
     * @return \DateTime|null
     */
    public function getNextUpdateDate()
    {
        if (!$this->last_materials_update) {
            return null;
        }
        $lastUpdate = new \DateTime($this->last_materials_update);
        $freq = $this->materials_update_frequency;
        if ($freq === 'daily') {
            $next = (clone $lastUpdate)->add(new \DateInterval('P1D'));
        } elseif ($freq === 'weekly') {
            $next = (clone $lastUpdate)->add(new \DateInterval('P7D'));
        } elseif ($freq === 'monthly') {
            $next = (clone $lastUpdate)->add(new \DateInterval('P1M'));
        } elseif ($freq === 'custom' && $this->custom_frequency_days) {
            $next = (clone $lastUpdate)->add(new \DateInterval('P' . (int)$this->custom_frequency_days . 'D'));
        } else {
            // Default to 7 days if invalid
            $next = (clone $lastUpdate)->add(new \DateInterval('P7D'));
        }
        return $next;
    }

    /**
     * Get the interval in days between last and next update.
     * @return int|null
     */
    public function getDaysInterval()
    {
        if (!$this->last_materials_update) {
            return null;
        }
        $lastUpdate = new \DateTime($this->last_materials_update);
        $nextUpdate = $this->getNextUpdateDate();
        if (!$nextUpdate) return null;
        return $lastUpdate->diff($nextUpdate)->days;
    }

    /**
     * Get the number of days for the current cycle.
     * - daily: 1
     * - weekly: 7
     * - monthly: days to same date next month
     * - custom: custom_frequency_days
     * @return int|null
     */
    public function getCycleDays()
    {
        $freq = $this->materials_update_frequency;
        if ($freq === 'daily') {
            return 1;
        } elseif ($freq === 'weekly') {
            return 7;
        } elseif ($freq === 'monthly') {
            // Calculate days to same date next month from last_materials_update or today
            $from = $this->last_materials_update ? new \DateTime($this->last_materials_update) : new \DateTime();
            $to = (clone $from)->add(new \DateInterval('P1M'));
            return $from->diff($to)->days;
        } elseif ($freq === 'custom' && $this->custom_frequency_days) {
            return (int)$this->custom_frequency_days;
        }
        return null;
    }

    /**
     * Get the courses associated with this account type
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function courses()
    {
        return $this->belongsToMany(Course::class, 'account_type_course', 'account_type_id', 'course_id');
    }

    /**
     * Get the union of all materials from all per-course lists for this account type
     *
     * @return array
     */
    public function getAllMaterialsListAttribute()
    {
        // Aggregate all lesson_list values from related AccountTypeCourseMaterial records
        $materials = $this->courseMaterials()->pluck('lesson_list')->toArray();
        $all = [];
        foreach ($materials as $list) {
            if (!empty($list)) {
                $all = array_merge($all, explode(',', $list));
            }
        }
        // Remove duplicates and empty values
        $all = array_unique(array_filter($all));
        return array_values($all);
    }

    /**
     * Relationship: per-course materials for this account type
     */
    public function courseMaterials()
    {
        return $this->hasMany(AccountTypeCourseMaterial::class, 'account_type_id');
    }

    /**
     * Generate prefixed title for a material based on its position
     *
     * @param string $originalTitle
     * @param int $position (1-based position in the list)
     * @return string
     */
    public function getPrefixedTitle($originalTitle, $position)
    {
        if (!$this->enable_title_prefix || empty($this->title_prefix_format)) {
            return $originalTitle;
        }

        // Strip any existing "Bài X - " prefix first
        $cleanTitle = preg_replace('/^Bài\s+\d+\s*-\s*/', '', $originalTitle);

        // Apply the new prefix
        $prefix = str_replace('{number}', $position, $this->title_prefix_format);
        return $prefix . $cleanTitle;
    }

    /**
     * Get the position of a material within its course context
     * This calculates the position based on materials from the same course that are in the account type's lesson_list
     *
     * @param string $materialContent
     * @param int $courseId
     * @return int
     */
    public function getMaterialPositionInCourse($materialContent, $courseId)
    {
        // Get all materials from this course that are in the account type's lesson_list
        $courseMaterials = \App\Models\Material::whereHas('lesson', function($query) use ($courseId) {
            $query->where('course_id', $courseId);
        })
        ->whereNull('deleted_at')
        ->get();

        $materialList = explode(',', $this->lesson_list);
        $courseMaterialsInList = [];

        foreach ($courseMaterials as $material) {
            if (in_array($material->content, $materialList)) {
                $courseMaterialsInList[] = $material->content;
            }
        }

        // Sort materials to ensure consistent ordering (natural sort for version-like numbers)
        usort($courseMaterialsInList, function($a, $b) {
            return version_compare($a, $b);
        });

        // Find position of the current material within this course
        $position = array_search($materialContent, $courseMaterialsInList);
        return $position !== false ? $position + 1 : 1;
    }
}
