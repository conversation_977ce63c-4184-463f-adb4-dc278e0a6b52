@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', __('messages.add.material'))

@section('menu_pagina')

    <li role="presentation">
        <a href="{{ route('material') }}" class="link_menu_page">
            <i class="fa fa-user"></i> {{ __('messages.index.material') }}
        </a>
    </li>

@endsection

@section('content')

    <div class="box box-primary">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('material.store') }}" method="post">
                        {{ csrf_field() }}
                        <input type="hidden" name="active" value="1">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('title') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.title') }}</label>
                                    <input type="text" name="title" class="form-control" placeholder="{{ __('messages.title') }}" required="" value="{{ old('title', $title) }}" autofocus>
                                    @if($errors->has('title'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('title') }}</strong>
                                        </span>
                                    @endif
                                    @if(request('content'))
                                        @php
                                            // Check if this content has prefix settings in any account type
                                            $content = request('content');
                                            $prefixInfo = [];
                                            $accountTypes = \App\Models\AccountType::where('enable_title_prefix', true)->get();

                                            foreach($accountTypes as $accountType) {
                                                $materials = explode(',', $accountType->lesson_list);
                                                if (in_array($content, $materials)) {
                                                    // Get course ID from request or try to determine it
                                                    $courseId = request('course_id');
                                                    if (!$courseId) {
                                                        // Try to find course from lesson if lesson_id is provided
                                                        $lessonId = request('lesson_id');
                                                        if ($lessonId) {
                                                            $lesson = \App\Models\Lesson::find($lessonId);
                                                            $courseId = $lesson ? $lesson->course_id : null;
                                                        }
                                                    }

                                                    if ($courseId) {
                                                        $position = $accountType->getMaterialPositionInCourse($content, $courseId);
                                                        $prefixedTitle = $accountType->getPrefixedTitle(old('title', $title), $position);
                                                        $prefixInfo[] = [
                                                            'account_type' => $accountType->name,
                                                            'prefixed_title' => $prefixedTitle
                                                        ];
                                                    }
                                                }
                                            }
                                        @endphp
                                        @if(!empty($prefixInfo))
                                            <div class="alert alert-info" style="margin-top: 10px; padding: 8px 12px; font-size: 12px;">
                                                <strong><i class="fa fa-info-circle"></i> Sẽ hiển thị với tiền tố:</strong><br>
                                                @foreach($prefixInfo as $info)
                                                    <small>{{ $info['account_type'] }}: <strong>{{ $info['prefixed_title'] }}</strong></small><br>
                                                @endforeach
                                            </div>
                                        @endif
                                    @endif
                                </div>
                            </div>
{{--                            <div class="col-lg-6">--}}
{{--                                <div class="form-group {{ $errors->has('type') ? 'has-error' : '' }}">--}}
{{--                                    <label for="nome">{{ __('messages.type') }}</label>--}}
{{--                                    <select name="type" id="type" class="form-control select2" data-placeholder="{{ __('messages.type') }}" required="">--}}
{{--                                        <option value="video"> video </option>--}}
{{--                                        <option value="document"> document </option>--}}
{{--                                    </select>--}}
{{--                                    @if($errors->has('type'))--}}
{{--                                        <span class="help-block">--}}
{{--                                            <strong>{{ $errors->first('type') }}</strong>--}}
{{--                                        </span>--}}
{{--                                    @endif--}}
{{--                                </div>--}}
{{--                            </div>--}}
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('content') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.content') }}</label>
                                    <input type="text" name="content" class="form-control" placeholder="{{ __('messages.content') }}" required="" value="{{ old('content', $content) }}" autofocus>
                                    @if($errors->has('content'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('content') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('content_url') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.content_url') }}</label>
                                    <input type="text" name="content_url" class="form-control" placeholder="{{ __('messages.content_url') }}" required="" value="{{ old('content_url') }}" autofocus>
                                    @if($errors->has('content_url'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('content_url') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('document_url') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.document_url') }}</label>
                                    <input type="text" name="document_url" class="form-control" placeholder="{{ __('messages.document_url') }}" required="" value="{{ old('document_url') }}" autofocus>
                                    @if($errors->has('document_url'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('document_url') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('lesson') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.lesson') }}</label>
                                    <select name="lesson" id="lesson" class="form-control select2" data-placeholder="{{ __('messages.lesson') }}" required="">
                                        @foreach($lessons as $lesson)
                                            <option value="{{ $lesson->id }}"> {{ $lesson->course->title_en . ' - ' . $lesson->title }} </option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('lesson'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('lesson') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label for="created_at">{{ __('messages.added_date') }}</label>
                                    <input type="text" class="form-control" value="{{ __('messages.will_be_set_to_current_date') }}" readonly>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <button type="submit" class="btn btn-primary pull-right"><i class="fa fa-fw fa-plus"></i> {{ __('messages.action.add') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('layout_js')

<script>
    $(function(){
        $('.select2').select2({
            "language": {
                "noResults": function(){
                    return "Nenhum registro encontrado.";
                }
            }
        });
    });

</script>

@endsection
