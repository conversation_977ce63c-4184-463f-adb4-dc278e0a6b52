@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', __('messages.edit.material'))

@section('menu_pagina')

    <li role="presentation">
        <a href="{{ route('material') }}" class="link_menu_page">
            <i class="fa fa-user"></i> {{ __('messages.index.material') }}
        </a>
    </li>

@endsection

@section('content')

    <div class="box box-primary">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('material.update', ['id' => $material->id]) }}" method="post">
                        {{ csrf_field() }}
                        <input type="hidden" name="_method" value="put">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('title') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.title') }}</label>
                                    <input type="text" name="title" class="form-control" placeholder="{{ __('messages.title') }}" required="" value="{{ $material->title }}" autofocus>
                                    @if($errors->has('title'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('title') }}</strong>
                                        </span>
                                    @endif
                                    @php
                                        // Check if this material has prefix settings in any account type
                                        $prefixInfo = [];
                                        $accountTypes = \App\Models\AccountType::where('enable_title_prefix', true)->get();

                                        foreach($accountTypes as $accountType) {
                                            $materials = explode(',', $accountType->lesson_list);
                                            if (in_array($material->content, $materials)) {
                                                $courseId = $material->lesson->course_id;
                                                $position = $accountType->getMaterialPositionInCourse($material->content, $courseId);
                                                $prefixedTitle = $accountType->getPrefixedTitle($material->title, $position);
                                                $prefixInfo[] = [
                                                    'account_type' => $accountType->name,
                                                    'prefixed_title' => $prefixedTitle
                                                ];
                                            }
                                        }
                                    @endphp
                                    @if(!empty($prefixInfo))
                                        <div class="alert alert-info" style="margin-top: 10px; padding: 8px 12px; font-size: 12px;">
                                            <strong><i class="fa fa-info-circle"></i> Hiển thị với tiền tố:</strong><br>
                                            @foreach($prefixInfo as $info)
                                                <small>{{ $info['account_type'] }}: <strong>{{ $info['prefixed_title'] }}</strong></small><br>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            </div>
{{--                            <div class="col-lg-6">--}}
{{--                                <div class="form-group {{ $errors->has('type') ? 'has-error' : '' }}">--}}
{{--                                    <label for="nome">{{ __('messages.type') }}</label>--}}
{{--                                    <select name="type" id="type" class="form-control select2" data-placeholder="{{ __('messages.type') }}" required="">--}}
{{--                                        <option value="video" @if("video" == $material->type) selected @endif> video </option>--}}
{{--                                        <option value="document" @if("document" == $material->type) selected @endif> document </option>--}}
{{--                                    </select>--}}
{{--                                    @if($errors->has('type'))--}}
{{--                                        <span class="help-block">--}}
{{--                                            <strong>{{ $errors->first('type') }}</strong>--}}
{{--                                        </span>--}}
{{--                                    @endif--}}
{{--                                </div>--}}
{{--                            </div>--}}
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('content') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.content') }}</label>
                                    <input type="text" name="content" class="form-control" placeholder="{{ __('messages.content') }}" required="" value="{{ $material->content }}" autofocus>
                                    @if($errors->has('content'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('content') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('content_url') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.content_url') }}</label>
                                    <input type="text" name="content_url" class="form-control" placeholder="{{ __('messages.content_url') }}" required="" value="{{ $material->content_url }}" autofocus>
                                    @if($errors->has('content_url'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('content_url') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('document_url') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.document_url') }}</label>
                                    <input type="text" name="document_url" class="form-control" placeholder="{{ __('messages.document_url') }}" required="" value="{{ $material->document_url }}" autofocus>
                                    @if($errors->has('document_url'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('document_url') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('lesson') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.lesson') }}</label>
                                    <select name="lesson" id="lesson" class="form-control select2" data-placeholder="{{ __('messages.lesson') }}" required="">
                                        @foreach($lessons as $lesson)
                                            <option value="{{ $lesson->id }}" @if($lesson->id == $material->lesson_id) selected @endif> {{ $lesson->course->title_en . ' - ' . $lesson->title }} </option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('lesson'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('lesson') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label for="created_at">{{ __('messages.added_date') }}</label>
                                    <input type="text" class="form-control" value="{{ $material->created_at ? $material->created_at->format('d-m-Y H:i:s') : '' }}" readonly>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <button type="submit" class="btn btn-primary pull-right"><i class="fa fa-fw fa-plus"></i> {{ __('messages.action.edit') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('layout_js')

<script>
    $(function(){
        $('.select2').select2({
            "language": {
                "noResults": function(){
                    return "Nenhum registro encontrado.";
                }
            }
        });
    });

</script>

@endsection
